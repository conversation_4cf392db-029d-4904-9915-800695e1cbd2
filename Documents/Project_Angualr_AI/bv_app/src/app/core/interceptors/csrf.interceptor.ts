import { HttpInterceptorFn, HttpRequest, HttpHandler } from '@angular/common/http';
import { inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { switchMap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';

export const csrfInterceptor: HttpInterceptorFn = (req, next) => {
  // Skip CSRF token for GET requests and external URLs
  if (req.method === 'GET' || !req.url.startsWith('/api/')) {
    return next(req);
  }

  // Check if CSRF token is already present
  if (req.headers.has('X-CSRFToken')) {
    return next(req);
  }

  const http = inject(HttpClient);

  // Get CSRF token from Django
  return http.get<{csrftoken: string}>('/api/auth/csrf/').pipe(
    switchMap(response => {
      const csrfToken = response.csrftoken;
      
      // Clone the request and add CSRF token
      const csrfReq = req.clone({
        setHeaders: {
          'X-CSRFToken': csrfToken
        }
      });
      
      return next(csrfReq);
    }),
    catchError(error => {
      console.error('Failed to get CSRF token:', error);
      // Continue with original request if CSRF token fetch fails
      return next(req);
    })
  );
};
