<!-- <PERSON><PERSON><PERSON> Doors Inventory Management System -->
<!-- Main Application Shell with Angular Material Design -->

<div class="app-container" [class.offline]="!isOnline()">
  <!-- Loading Spinner for Initial App Load -->
  @if (isLoading()) {
    <div class="app-loading" role="status" aria-label="Loading application">
      <div class="loading-content">
        <!-- Company Logo/Icon -->
        <div class="app-logo">
          <mat-icon class="logo-icon">store</mat-icon>
          <h1 class="app-title">Bhavani Doors</h1>
          <p class="app-subtitle">Inventory Management System</p>
        </div>

        <!-- Loading Spinner -->
        <mat-spinner diameter="50" aria-label="Loading"></mat-spinner>
        <p class="loading-text">Initializing application...</p>

        <!-- Loading Progress Indicator -->
        <div class="loading-steps">
          <div class="step" [class.active]="loadingStep() >= 1">
            <mat-icon>check_circle</mat-icon>
            <span>Connecting to server</span>
          </div>
          <div class="step" [class.active]="loadingStep() >= 2">
            <mat-icon>check_circle</mat-icon>
            <span>Loading user preferences</span>
          </div>
          <div class="step" [class.active]="loadingStep() >= 3">
            <mat-icon>check_circle</mat-icon>
            <span>Preparing dashboard</span>
          </div>
        </div>
      </div>
    </div>
  } @else {
    <!-- Main Application Content -->
    <app-navbar></app-navbar>
  }
</div>

<!-- Offline Indicator -->
@if (!isOnline()) {
  <div class="offline-banner" role="alert" aria-live="polite">
    <mat-icon>wifi_off</mat-icon>
    <span>You are currently offline. Some features may not be available.</span>
    <button mat-button class="retry-button" (click)="checkConnection()" aria-label="Retry connection">
      <mat-icon>refresh</mat-icon>
      Retry
    </button>
  </div>
}

<!-- Global Error Handler -->
@if (globalError()) {
  <div class="error-banner" role="alert" aria-live="assertive">
    <mat-icon>error</mat-icon>
    <span>{{ globalError() }}</span>
    <button mat-button (click)="dismissError()" aria-label="Dismiss error">
      <mat-icon>close</mat-icon>
    </button>
  </div>
}

<!-- Accessibility Skip Links -->
<div class="skip-links">
  <a class="skip-link" href="#main-content">Skip to main content</a>
  <a class="skip-link" href="#navigation">Skip to navigation</a>
</div>