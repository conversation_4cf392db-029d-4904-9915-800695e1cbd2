import { Component, OnInit, On<PERSON><PERSON>roy, signal } from '@angular/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NavbarComponent } from './navbar/navbar.component';

@Component({
  selector: 'app-root',
  imports: [
    MatProgressSpinnerModule,
    MatIconModule,
    MatButtonModule,
    NavbarComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements OnInit, OnDestroy {
  protected title = 'Bhavani Doors Admin';

  // Application state signals
  protected isLoading = signal(true);
  protected isOnline = signal(navigator.onLine);
  protected loadingStep = signal(0);
  protected globalError = signal<string | null>(null);

  private loadingInterval?: number;

  ngOnInit() {
    this.startLoadingSequence();
    this.setupNetworkListeners();
  }

  ngOnD<PERSON>roy() {
    if (this.loadingInterval) {
      clearInterval(this.loadingInterval);
    }
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  }

  private startLoadingSequence() {
    // Simulate progressive loading steps
    const steps = [
      { step: 1, delay: 500, message: 'Connecting to server' },
      { step: 2, delay: 1000, message: 'Loading user preferences' },
      { step: 3, delay: 1500, message: 'Preparing dashboard' }
    ];

    steps.forEach(({ step, delay }) => {
      setTimeout(() => {
        this.loadingStep.set(step);
      }, delay);
    });

    // Complete loading
    setTimeout(() => {
      this.isLoading.set(false);
    }, 2000);
  }

  private setupNetworkListeners() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  private handleOnline = () => {
    this.isOnline.set(true);
    this.globalError.set(null);
  };

  private handleOffline = () => {
    this.isOnline.set(false);
  };

  protected checkConnection() {
    // Force check network status
    this.isOnline.set(navigator.onLine);

    if (navigator.onLine) {
      // Try to make a simple network request to verify connectivity
      fetch('/api/health', { method: 'HEAD' })
        .then(() => {
          this.globalError.set(null);
        })
        .catch(() => {
          this.globalError.set('Unable to connect to server. Please check your internet connection.');
        });
    }
  }

  protected dismissError() {
    this.globalError.set(null);
  }
}
