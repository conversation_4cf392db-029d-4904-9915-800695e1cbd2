import { Component, inject, computed } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { RouterModule } from '@angular/router';
import { Auth } from '../core/auth/auth';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss',
  imports: [
    CommonModule,
    MatToolbarModule,
    MatButtonModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    RouterModule,
  ]
})
export class NavbarComponent {
  private breakpointObserver = inject(BreakpointObserver);
  private auth = inject(Auth);

  // Signal-based responsive detection
  protected readonly isHandset = computed(() =>
    this.breakpointObserver.isMatched(Breakpoints.Handset)
  );

  // Auth signals
  protected readonly user = this.auth.user;
  protected readonly isAuthenticated = this.auth.isAuthenticated;

  protected readonly menuItems = [
    { icon: 'dashboard', label: 'Dashboard', route: '/dashboard' },
    { icon: 'inventory_2', label: 'Materials', route: '/materials' },
    { icon: 'local_shipping', label: 'Suppliers', route: '/suppliers' },
    { icon: 'people', label: 'Customers', route: '/customers' },
    { icon: 'shopping_cart', label: 'Orders', route: '/orders' },
    { icon: 'receipt', label: 'GST', route: '/gst' },
    { icon: 'assessment', label: 'Reports', route: '/reports' },
  ];

  logout(): void {
    this.auth.logout();
  }
}
