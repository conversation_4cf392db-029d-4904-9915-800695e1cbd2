// Bhavani Doors App Root Styles
// Following Angular Material Design Guidelines

@use '@angular/material' as mat;

// App Container
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  background-color: var(--mat-sys-surface);
  color: var(--mat-sys-on-surface);
}

// Loading Screen Styles
.app-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg,
    var(--mat-sys-primary-container) 0%,
    var(--mat-sys-surface) 100%);
  padding: 20px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 400px;
    width: 100%;
  }

  .app-logo {
    text-align: center;
    margin-bottom: 32px;

    .logo-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: var(--mat-sys-primary);
      margin-bottom: 16px;
    }

    .app-title {
      font-family: var(--mat-sys-display-small-font);
      font-size: var(--mat-sys-display-small-size);
      font-weight: var(--mat-sys-display-small-weight);
      color: var(--mat-sys-on-surface);
      margin: 0 0 8px 0;
    }

    .app-subtitle {
      font-family: var(--mat-sys-title-medium-font);
      font-size: var(--mat-sys-title-medium-size);
      color: var(--mat-sys-on-surface-variant);
      margin: 0;
    }
  }

  mat-spinner {
    margin: 24px 0;

    ::ng-deep circle {
      stroke: var(--mat-sys-primary);
    }
  }

  .loading-text {
    font-family: var(--mat-sys-body-large-font);
    font-size: var(--mat-sys-body-large-size);
    font-weight: var(--mat-sys-body-large-weight);
    color: var(--mat-sys-on-surface);
    margin: 0 0 32px 0;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
  }

  .loading-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;

    .step {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      border-radius: 8px;
      background-color: var(--mat-sys-surface-variant);
      color: var(--mat-sys-on-surface-variant);
      transition: all 0.3s ease;
      opacity: 0.5;

      &.active {
        opacity: 1;
        background-color: var(--mat-sys-primary-container);
        color: var(--mat-sys-on-primary-container);

        mat-icon {
          color: var(--mat-sys-primary);
        }
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      span {
        font-family: var(--mat-sys-body-medium-font);
        font-size: var(--mat-sys-body-medium-size);
      }
    }
  }
}

// Loading Animation
@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

// Global Snackbar Container
.snackbar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

// Offline Banner
.offline-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--mat-sys-error-container);
  color: var(--mat-sys-on-error-container);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1001;
  box-shadow: var(--mat-sys-elevation-level2);
  border-bottom: 1px solid var(--mat-sys-outline-variant);
  animation: slideDown 0.3s ease-out;

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  span {
    font-family: var(--mat-sys-body-medium-font);
    font-size: var(--mat-sys-body-medium-size);
    font-weight: var(--mat-sys-body-medium-weight);
    flex: 1;
  }

  .retry-button {
    margin-left: auto;

    mat-icon {
      margin-right: 4px;
    }
  }
}

// Error Banner
.error-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--mat-sys-error);
  color: var(--mat-sys-on-error);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1002;
  box-shadow: var(--mat-sys-elevation-level3);
  animation: slideDown 0.3s ease-out;

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  span {
    font-family: var(--mat-sys-body-medium-font);
    font-size: var(--mat-sys-body-medium-size);
    font-weight: var(--mat-sys-body-medium-weight);
    flex: 1;
  }

  button {
    margin-left: auto;
    color: var(--mat-sys-on-error);

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

// Skip Links for Accessibility
.skip-links {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 2000;

  .skip-link {
    position: absolute;
    top: 0;
    left: 0;
    background-color: var(--mat-sys-primary);
    color: var(--mat-sys-on-primary);
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 0 0 4px 0;
    font-family: var(--mat-sys-body-medium-font);
    font-size: var(--mat-sys-body-medium-size);
    font-weight: var(--mat-sys-body-medium-weight);
    transition: top 0.3s ease;

    &:focus {
      top: 0;
    }

    &:nth-child(2) {
      left: 140px;
    }
  }
}

// Slide Down Animation
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .app-loading {
    padding: 16px;

    .app-logo {
      margin-bottom: 24px;

      .logo-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
      }

      .app-title {
        font-size: 24px;
      }

      .app-subtitle {
        font-size: 16px;
      }
    }

    .loading-text {
      font-size: 16px;
      margin-bottom: 24px;
    }

    .loading-steps {
      .step {
        padding: 6px 12px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }

  .offline-banner,
  .error-banner {
    padding: 8px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    span {
      font-size: 14px;
    }

    button {
      margin-left: 0;
      align-self: flex-end;
    }
  }

  .skip-links {
    .skip-link {
      font-size: 14px;
      padding: 6px 12px;

      &:nth-child(2) {
        left: 120px;
      }
    }
  }
}

// Extra small screens
@media (max-width: 480px) {
  .app-loading {
    .app-logo {
      .app-title {
        font-size: 20px;
      }

      .app-subtitle {
        font-size: 14px;
      }
    }

    .loading-steps {
      .step {
        span {
          font-size: 13px;
        }
      }
    }
  }

  .offline-banner,
  .error-banner {
    span {
      font-size: 13px;
    }
  }
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .app-loading {
    background: var(--mat-sys-surface);
    border: 2px solid var(--mat-sys-outline);
  }

  .offline-banner {
    border: 2px solid var(--mat-sys-error);
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  .loading-text {
    animation: none;
  }

  mat-spinner {
    ::ng-deep circle {
      animation-duration: 4s;
    }
  }
}

// Print Styles
@media print {
  .app-loading,
  .offline-banner,
  .error-banner,
  .skip-links {
    display: none;
  }
}

// App Container States
.app-container {
  &.offline {
    // Add visual indication when offline
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: repeating-linear-gradient(
        90deg,
        var(--mat-sys-error) 0px,
        var(--mat-sys-error) 10px,
        transparent 10px,
        transparent 20px
      );
      z-index: 1000;
      animation: offline-indicator 2s linear infinite;
    }
  }
}

// Offline Indicator Animation
@keyframes offline-indicator {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}

// Focus Management
.app-container {
  &:focus-within {
    .skip-links .skip-link:focus {
      outline: 2px solid var(--mat-sys-primary);
      outline-offset: 2px;
    }
  }
}

// Dark Mode Specific Adjustments
@media (prefers-color-scheme: dark) {
  .app-loading {
    background: linear-gradient(135deg,
      var(--mat-sys-primary-container) 0%,
      var(--mat-sys-surface-dim) 100%);
  }
}

// High DPI Displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .app-loading {
    .app-logo .logo-icon {
      // Ensure crisp icons on high DPI displays
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }
}